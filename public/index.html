<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <meta name="description"
      content="Professional blockchain dashboard for Metawave tokens with MetaMask integration, real-time analytics, and secure token trading.">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet">

    <link rel="icon" type="image/png" sizes="16x16"
      href="/metwave_favicon_16x16.png">
    <link rel="icon" type="image/png" sizes="32x32"
      href="/metwave_favicon_32x32.png">
    <link rel="icon" type="image/png" sizes="64x64"
      href="/metwave_favicon_64x64.png">
    <link rel="icon" type="image/png" sizes="128x128"
      href="/metwave_favicon_128x128.png">
    <link rel="icon" type="image/png" sizes="256x256"
      href="/metwave_favicon_256x256.png">
    <link rel="icon" type="image/png" sizes="512x512"
      href="/metwave_favicon_512x512.png">
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Metwave Dashboard - Blockchain Token Managemen</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
