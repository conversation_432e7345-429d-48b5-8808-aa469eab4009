{"name": "metwavetoken-frontend", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-scripts": "5.0.1", "redux-saga": "^1.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "wouter": "^3.7.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}