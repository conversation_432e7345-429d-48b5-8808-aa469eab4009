import { Switch, Route } from "wouter";
import { Provider } from "react-redux";
import { store } from "./store";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Dashboard from "@/pages/dashboard";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Dashboard} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <Provider store={store}>
      <TooltipProvider>
        <div className="min-h-screen bg-crypto-dark text-white">
          <Toaster />
          <Router />
        </div>
      </TooltipProvider>
    </Provider>
  );
}

export default App;
