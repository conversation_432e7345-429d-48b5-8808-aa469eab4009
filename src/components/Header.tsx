import { useMetaMask } from "@/hooks/useMetaMask";
import { useMultiWallet } from "@/hooks/useMultiWallet";
import { MultiWalletButton } from "./MultiWalletButton";
import logo from "@assets/logo.png";

export function Header() {
  const { account, isConnected } = useMetaMask();

  return (
    <header className="bg-crypto-gray border-b border-gray-700">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8">
        <div className="flex justify-between items-center h-14 sm:h-16">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center">
              <img
                src={logo}
                alt="METWAVE Logo"
                className="w-full h-full object-contain filter invert brightness-100"
              />
            </div>
            <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-crypto-gold">METWAVE</h1>
          </div>

          <div className="flex items-center">
            <MultiWalletButton />
          </div>
        </div>
      </div>
    </header>
  );
}
