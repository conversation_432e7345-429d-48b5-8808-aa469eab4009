
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Wallet, ChevronDown, Copy, ExternalLink } from "lucide-react";
import { useMultiWallet } from "@/hooks/useMultiWallet";
import { useToast } from "@/hooks/use-toast";
import { WalletSelectionModal } from "@/components/WalletSelectionModal";
import { WalletDisconnectModal } from "@/components/WalletDisconnectModal";
import { shortenAddress } from "@/lib/utils";

export function MultiWalletButton() {
  const { 
    isConnected, 
    account, 
    provider,
    isConnecting, 
    error,
    showWalletModal,
    openWalletModal,
    closeWalletModal,
    connectWallet,
    disconnectWallet
  } = useMultiWallet();
  
  const { toast } = useToast();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showDisconnectModal, setShowDisconnectModal] = useState(false);

  const handleCopyAddress = () => {
    if (account) {
      navigator.clipboard.writeText(account);
      toast({
        title: "Address Copied",
        description: "Wallet address copied to clipboard",
      });
    }
  };

  const handleViewOnExplorer = () => {
    if (account) {
      window.open(`https://etherscan.io/address/${account}`, '_blank');
    }
  };

  if (!isConnected) {
    return (
      <>
        <Button 
          onClick={openWalletModal}
          disabled={isConnecting}
          className="relative bg-crypto-card hover:bg-crypto-card-hover border border-crypto-border text-crypto-gold font-semibold px-6 py-2.5 rounded-lg transition-all duration-300 hover:border-crypto-gold/50 hover:shadow-lg hover:shadow-crypto-gold/20"
        >
          {isConnecting ? (
            <div className="flex items-center">
              <div className="w-4 h-4 border-2 border-crypto-gold border-t-transparent rounded-full animate-spin mr-2" />
              Connecting...
            </div>
          ) : (
            <div className="flex items-center">
              <Wallet className="h-4 w-4 mr-2" />
              Connect Wallet
            </div>
          )}
        </Button>

        <WalletSelectionModal
          isOpen={showWalletModal}
          onClose={closeWalletModal}
          onWalletSelect={connectWallet}
          isConnecting={isConnecting}
          error={error}
        />
      </>
    );
  }

  return (
    <div className="relative">
      <Button
        onClick={() => setShowDropdown(!showDropdown)}
        className="bg-crypto-card hover:bg-crypto-card-hover border border-crypto-border text-white px-4 py-2 rounded-lg transition-all duration-200 hover:border-crypto-gold/50"
      >
        <div className="flex items-center gap-2">
          {provider && <span className="text-lg">{provider.icon}</span>}
          <span className="font-mono text-sm text-crypto-gold">{shortenAddress(account!)}</span>
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>
      </Button>

      {showDropdown && (
        <div className="absolute right-0 top-full mt-2 w-72 bg-crypto-dark border border-crypto-border rounded-lg shadow-2xl z-[100]">
          <div className="p-4 space-y-4">
            <div className="flex items-center gap-3 pb-3 border-b border-crypto-border">
              {provider && <span className="text-xl">{provider.icon}</span>}
              <div className="flex-1">
                <div className="text-sm font-medium text-white">{provider?.name}</div>
                <div className="text-xs text-gray-400">Connected wallet</div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Address</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-mono text-crypto-gold">{shortenAddress(account!)}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyAddress}
                    className="h-6 w-6 p-0 hover:bg-crypto-card text-gray-400 hover:text-crypto-gold"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleViewOnExplorer}
                    className="h-6 w-6 p-0 hover:bg-crypto-card text-gray-400 hover:text-crypto-gold"
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="border-t border-crypto-border pt-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDropdown(false);
                  setShowDisconnectModal(true);
                }}
                className="w-full text-sm border-crypto-border text-white hover:border-red-400 hover:text-red-400 transition-all duration-200"
              >
                Disconnect
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {showDropdown && (
        <div 
          className="fixed inset-0 z-[90] bg-black/20 backdrop-blur-[2px]" 
          onClick={() => setShowDropdown(false)}
        />
      )}

      <WalletSelectionModal
        isOpen={showWalletModal}
        onClose={closeWalletModal}
        onWalletSelect={connectWallet}
        isConnecting={isConnecting}
        error={error}
      />

      <WalletDisconnectModal
        isOpen={showDisconnectModal}
        onClose={() => setShowDisconnectModal(false)}
        onConfirm={disconnectWallet}
        walletName={provider?.name || 'wallet'}
      />
    </div>
  );
}
