import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToken, useTokenAction } from "@/hooks/useTokenData";
import { useMetaMask } from "@/hooks/useMetaMask";
import { formatNumber, formatCurrency } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import blackTokenImage from "@assets/BLACKTOKEN.png";
import greenTokenImage from "@assets/GREENTOKEN.png";

interface TokenCardProps {
  tokenType: "black" | "green";
}

export function TokenCard({ tokenType }: TokenCardProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const [actionAmount, setActionAmount] = useState("1");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<
    "buy" | "sell" | "trade" | "burn" | null
  >(null);
  const { data: token, isLoading } = useToken(tokenType);
  const { account, isConnected } = useMetaMask();
  const tokenAction = useTokenAction();
  const { toast } = useToast();

  const handleFlip = () => {
    setIsFlipped(true);
    setTimeout(() => setIsFlipped(false), 600);
  };

  const openActionDialog = (action: "buy" | "sell" | "trade" | "burn") => {
    if (!isConnected || !account || !token) {
      toast({
        title: "Wallet Not Connected",
        description:
          "Please connect your MetaMask wallet to perform transactions.",
        variant: "destructive",
      });
      return;
    }
    setSelectedAction(action);
    setActionAmount("1");
    setIsDialogOpen(true);
  };

  const handleAction = async () => {
    if (!selectedAction || !token || !account) return;

    try {
      const result = await tokenAction.mutateAsync({
        tokenId: token.id,
        action: selectedAction,
        walletAddress: account,
        amount: actionAmount,
      });

      toast({
        title: `${
          selectedAction.charAt(0).toUpperCase() + selectedAction.slice(1)
        } Completed`,
        description: `Successfully ${
          selectedAction === "buy"
            ? "purchased"
            : selectedAction === "sell"
            ? "sold"
            : selectedAction === "trade"
            ? "traded"
            : "burned"
        } ${actionAmount} ${token.name} tokens.`,
      });

      setIsDialogOpen(false);
      setSelectedAction(null);
    } catch (error: any) {
      toast({
        title: "Transaction Failed",
        description:
          error.message ||
          `Failed to ${selectedAction} ${token.name}. Please try again.`,
        variant: "destructive",
      });
    }
  };

  if (isLoading || !token) {
    return (
      <div className="gradient-border animate-pulse">
        <div className="gradient-border-content p-6 h-80">
          <div className="w-24 h-24 bg-gray-700 rounded-full mx-auto mb-6"></div>
          <div className="h-8 bg-gray-700 rounded mb-6"></div>
          <div className="grid grid-cols-3 gap-4 mb-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-4 gap-2">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Create token icon component using images
  const TokenIcon = ({
    type,
    isFlipped,
  }: {
    type: "black" | "green";
    isFlipped: boolean;
  }) => (
    <div
      className={`w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 cursor-pointer transition-transform duration-300 hover:scale-105 ${
        isFlipped ? "animate-spin" : ""
      }`}
    >
      <img
        src={type === "black" ? blackTokenImage : greenTokenImage}
        alt={`${type === "black" ? "Black" : "Green"} Token`}
        className="w-full h-full object-contain"
      />
    </div>
  );

  return (
    <div className="bg-crypto-gray rounded-lg p-4 sm:p-6 lg:p-8 border border-gray-700">
      <div className="flex flex-col items-center">
        {/* Token Icon */}
        <div onClick={handleFlip} className="mb-4 sm:mb-6">
          <TokenIcon type={tokenType} isFlipped={isFlipped} />
        </div>

        {/* Token Name */}
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-4 sm:mb-6 lg:mb-8">
          {token.name}
        </h2>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8 w-full">
          <div className="text-center p-3 sm:p-0 bg-gray-800 sm:bg-transparent rounded-lg sm:rounded-none">
            <div className="text-gray-400 text-xs sm:text-sm mb-1 sm:mb-2">
              Total Tokens
            </div>
            <div className="text-lg sm:text-xl lg:text-2xl font-bold text-white">
              {formatNumber(token.totalSupply, 0)}
            </div>
          </div>
          <div className="text-center p-3 sm:p-0 bg-gray-800 sm:bg-transparent rounded-lg sm:rounded-none">
            <div className="text-gray-400 text-xs sm:text-sm mb-1 sm:mb-2">
              Total Burned
            </div>
            <div className="text-lg sm:text-xl lg:text-2xl font-bold text-white">
              {formatNumber(token.totalBurned, 0)}
            </div>
          </div>
          <div className="text-center p-3 sm:p-0 bg-gray-800 sm:bg-transparent rounded-lg sm:rounded-none">
            <div className="text-gray-400 text-xs sm:text-sm mb-1 sm:mb-2">
              Value
            </div>
            <div className="text-lg sm:text-xl lg:text-2xl font-bold text-white">
              {formatCurrency(token.currentPrice)}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 w-full">
          <Button
            size="sm"
            className="bg-transparent text-crypto-gold border border-crypto-gold hover:bg-crypto-gold hover:text-black font-medium transition-all duration-200 text-xs sm:text-sm py-2 sm:py-3"
            onClick={() => openActionDialog("buy")}
            disabled={tokenAction.isPending || !isConnected}
          >
            Buy
          </Button>
          <Button
            size="sm"
            className="bg-transparent text-crypto-gold border border-crypto-gold hover:bg-crypto-gold hover:text-black font-medium transition-all duration-200 text-xs sm:text-sm py-2 sm:py-3"
            onClick={() => openActionDialog("sell")}
            disabled={tokenAction.isPending || !isConnected}
          >
            Sell
          </Button>
          <Button
            size="sm"
            className="bg-transparent text-crypto-gold border border-crypto-gold hover:bg-crypto-gold hover:text-black font-medium transition-all duration-200 text-xs sm:text-sm py-2 sm:py-3"
            onClick={() => openActionDialog("trade")}
            disabled={tokenAction.isPending || !isConnected}
          >
            Trade
          </Button>
          <Button
            size="sm"
            className="bg-transparent text-crypto-gold border border-crypto-gold hover:bg-crypto-gold hover:text-black font-medium transition-all duration-200 text-xs sm:text-sm py-2 sm:py-3"
            onClick={() => openActionDialog("burn")}
            disabled={tokenAction.isPending || !isConnected}
          >
            Burn
          </Button>
        </div>

        {/* Action Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="bg-crypto-gray border-crypto-gold w-[95vw] max-w-md mx-auto sm:w-full">
            <DialogHeader className="pb-4">
              <DialogTitle className="text-crypto-gold text-lg sm:text-xl text-center sm:text-left">
                {selectedAction &&
                  `${
                    selectedAction.charAt(0).toUpperCase() +
                    selectedAction.slice(1)
                  } ${token.name}`}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4 sm:space-y-6">
              <div>
                <label className="text-sm sm:text-base text-gray-400 mb-2 sm:mb-3 block font-medium">
                  Amount
                </label>
                <Input
                  type="number"
                  value={actionAmount}
                  onChange={(e) => setActionAmount(e.target.value)}
                  className="bg-crypto-dark border-gray-600 text-white h-12 sm:h-10 text-base sm:text-sm"
                  placeholder="Enter amount"
                  min="0.01"
                  step="0.01"
                />
              </div>
              {selectedAction && (
                <div className="text-xs sm:text-sm text-gray-400 p-3 sm:p-2 bg-gray-800 rounded-lg border border-gray-700">
                  {selectedAction === "buy" &&
                    `Cost: ${formatCurrency(
                      (
                        parseFloat(actionAmount) *
                        parseFloat(token.currentPrice)
                      ).toString()
                    )}`}
                  {selectedAction === "sell" &&
                    `You will receive: ${formatCurrency(
                      (
                        parseFloat(actionAmount) *
                        parseFloat(token.currentPrice)
                      ).toString()
                    )}`}
                  {selectedAction === "trade" &&
                    `You will receive: ${(
                      parseFloat(actionAmount) * 0.95
                    ).toFixed(2)} ${
                      tokenType === "black" ? "Green" : "Black"
                    } tokens (5% fee)`}
                  {selectedAction === "burn" &&
                    `${actionAmount} tokens will be permanently destroyed`}
                </div>
              )}
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-2 pt-2">
                <Button
                  onClick={handleAction}
                  disabled={
                    tokenAction.isPending ||
                    !actionAmount ||
                    parseFloat(actionAmount) <= 0
                  }
                  className="flex-1 bg-crypto-gold text-black hover:bg-yellow-500 h-12 sm:h-10 text-base sm:text-sm font-medium"
                >
                  {tokenAction.isPending
                    ? "Processing..."
                    : `Confirm ${selectedAction}`}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1 border-gray-600 text-gray-300 hover:text-white hover:bg-gray-700 h-12 sm:h-10 text-base sm:text-sm font-medium"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
