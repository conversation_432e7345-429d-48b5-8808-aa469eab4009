import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, ExternalLink } from "lucide-react";

interface WalletDisconnectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  walletName: string;
}

export function WalletDisconnectModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  walletName 
}: WalletDisconnectModalProps) {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] bg-crypto-dark border-crypto-border">
        <DialogHeader>
          <DialogTitle className="text-crypto-gold flex items-center gap-2">
            <Info className="h-5 w-5" />
            Disconnect Wallet
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Disconnect {walletName} from METWAVE
          </DialogDescription>
        </DialogHeader>

        <Alert className="border-blue-500/20 bg-blue-500/10">
          <Info className="h-4 w-4 text-blue-400" />
          <AlertDescription className="text-blue-300">
            <strong>How wallet disconnection works:</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• This will disconnect your wallet from METWAVE only</li>
              <li>• Your wallet extension will remain unlocked and connected to other sites</li>
              <li>• To fully disconnect, you'll need to lock your wallet extension manually</li>
              <li>• Your transaction history and balances are not affected</li>
            </ul>
          </AlertDescription>
        </Alert>

        <div className="space-y-3">
          <p className="text-sm text-gray-300">
            After disconnecting, you can reconnect anytime by clicking "Connect Wallet" and selecting {walletName} again.
          </p>
          
          <div className="bg-crypto-card/50 p-3 rounded-lg border border-crypto-border">
            <p className="text-xs text-gray-400 mb-2">
              Need help managing your wallet connection?
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (walletName === 'MetaMask') {
                  window.open('https://support.metamask.io/hc/en-us/articles/360015488531-Getting-started-with-MetaMask', '_blank');
                } else {
                  window.open('https://ethereum.org/en/wallets/', '_blank');
                }
              }}
              className="text-xs border-crypto-gold/30 text-crypto-gold hover:border-crypto-gold"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Learn More
            </Button>
          </div>
        </div>

        <div className="flex gap-2 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1 border-gray-600 text-gray-400 hover:border-gray-500"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="flex-1 bg-red-600 hover:bg-red-700 text-white"
          >
            Disconnect from METWAVE
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}