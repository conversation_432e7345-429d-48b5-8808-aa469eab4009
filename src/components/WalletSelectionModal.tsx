import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Wallet } from "lucide-react";
import { getAllWalletProviders } from "@/lib/walletProviders";
import type { WalletProvider } from "@/types/wallet";

interface WalletSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onWalletSelect: (provider: WalletProvider) => void;
  isConnecting: boolean;
  error: string | null;
}

export function WalletSelectionModal({ 
  isOpen, 
  onClose, 
  onWalletSelect, 
  isConnecting, 
  error 
}: WalletSelectionModalProps) {
  const walletProviders = getAllWalletProviders();
  const installedWallets = walletProviders.filter(wallet => wallet.isInstalled);
  const availableWallets = walletProviders.filter(wallet => wallet.isAvailable && !wallet.isInstalled);
  const unavailableWallets = walletProviders.filter(wallet => !wallet.isAvailable);

  const handleWalletClick = (provider: WalletProvider) => {
    if (!provider.isInstalled && provider.id !== 'walletconnect') {
      // Open installation page for the wallet
      const installUrls: Record<string, string> = {
        metamask: 'https://metamask.io/download/',
        coinbase: 'https://www.coinbase.com/wallet',
        trust: 'https://trustwallet.com/browser-extension',
        phantom: 'https://phantom.app/',
        rabby: 'https://rabby.io/',
      };
      
      if (installUrls[provider.id]) {
        window.open(installUrls[provider.id], '_blank');
        return;
      }
    }
    
    onWalletSelect(provider);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[480px] bg-crypto-dark border border-crypto-border shadow-2xl">
        <DialogHeader className="text-center pb-4">
          <div className="mx-auto w-12 h-12 bg-crypto-card border border-crypto-border rounded-lg flex items-center justify-center mb-4">
            <Wallet className="h-6 w-6 text-crypto-gold" />
          </div>
          <DialogTitle className="text-xl font-semibold text-crypto-gold">
            Connect Wallet
          </DialogTitle>
          <DialogDescription className="text-gray-400 text-sm">
            Choose your preferred wallet to access METWAVE
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert className="border-red-500/20 bg-red-500/10">
            <AlertDescription className="text-red-400">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-4">
          {/* Installed Wallets */}
          {installedWallets.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-crypto-gold mb-2">Installed Wallets</h4>
              <div className="space-y-2">
                {installedWallets.map((wallet) => (
                  <Button
                    key={wallet.id}
                    onClick={() => handleWalletClick(wallet)}
                    disabled={isConnecting}
                    className="w-full justify-start bg-crypto-card hover:bg-crypto-card-hover border border-crypto-border hover:border-crypto-gold/50 transition-all duration-200 p-4 h-auto group"
                  >
                    <span className="text-xl mr-3">{wallet.icon}</span>
                    <div className="flex-1 text-left">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-white font-medium">{wallet.name}</span>
                        <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                          Ready
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-400">{wallet.description}</p>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Available but not installed */}
          {availableWallets.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-crypto-gold mb-2">Available Wallets</h4>
              <div className="space-y-2">
                {availableWallets.map((wallet) => (
                  <Button
                    key={wallet.id}
                    onClick={() => handleWalletClick(wallet)}
                    disabled={isConnecting}
                    className="w-full justify-start bg-crypto-card/50 hover:bg-crypto-card-hover border border-crypto-border hover:border-crypto-gold/50 transition-all duration-200 p-4 h-auto"
                  >
                    <span className="text-xl mr-3 opacity-80">{wallet.icon}</span>
                    <div className="flex-1 text-left">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-white font-medium">{wallet.name}</span>
                        <Badge variant="outline" className="border-crypto-gold/30 text-crypto-gold text-xs">
                          Available
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-400">{wallet.description}</p>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Unavailable wallets (install required) */}
          {unavailableWallets.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-2">Install to Use</h4>
              <div className="space-y-2">
                {unavailableWallets.map((wallet) => (
                  <Button
                    key={wallet.id}
                    onClick={() => handleWalletClick(wallet)}
                    variant="outline"
                    className="w-full justify-start border-gray-600/50 text-gray-400 hover:border-gray-500 hover:bg-crypto-card/30 transition-all duration-200 p-4 h-auto"
                  >
                    <span className="text-xl mr-3 opacity-50">{wallet.icon}</span>
                    <div className="flex-1 text-left">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{wallet.name}</span>
                        <Badge variant="outline" className="border-gray-600 text-gray-500 text-xs">
                          Install Required
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500">{wallet.description}</p>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="pt-4 border-t border-crypto-border">
          <p className="text-xs text-gray-500 text-center">
            New to wallets? Learn more about{" "}
            <a 
              href="https://ethereum.org/en/wallets/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-crypto-gold hover:underline"
            >
              crypto wallets
            </a>
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}