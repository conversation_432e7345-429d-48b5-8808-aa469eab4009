import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON>Axis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  useTotalRevenue,
  useTotalCarbonSaved,
  useAnalytics,
} from "@/hooks/useTokenData";
import { formatCurrency, formatLargeNumber } from "@/lib/utils";
import { TrendingUp, BarChart3, Leaf } from "lucide-react";

export function AnalyticsSection() {
  const { data: revenue } = useTotalRevenue();
  const { data: carbon } = useTotalCarbonSaved();
  const { data: blackAnalytics = [] } = useAnalytics(1);
  const { data: greenAnalytics = [] } = useAnalytics(2);

  // Prepare chart data
  const chartData = blackAnalytics.map((blackItem, index) => {
    const greenItem = greenAnalytics[index];
    const date = new Date(blackItem.date);

    return {
      month: date.toLocaleDateString("en-US", { month: "short" }),
      blackRevenue: parseFloat(blackItem.revenue),
      greenRevenue: greenItem ? parseFloat(greenItem.revenue) : 0,
    };
  });

  return (
    <div
      className="gradient-border h-full animate-slide-up"
      style={{ animationDelay: "0.4s" }}
    >
      <div className="gradient-border-content p-6">
        <h3 className="text-xl font-bold mb-6 flex items-center">
          <BarChart3 className="text-crypto-gold mr-2 h-5 w-5" />
          Analytics & Reporting
        </h3>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Revenue Earned by Token Type */}
          <div>
            <h4 className="text-lg font-semibold mb-4 flex items-center">
              <TrendingUp className="w-4 h-4 mr-2 text-crypto-gold" />
              Revenue Earned by Token Type
            </h4>
            <div className="flex items-center space-x-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {revenue ? formatLargeNumber(revenue.blackRevenue) : "$0"}
                </div>
                <div className="text-sm text-gray-400">Black</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {revenue ? formatLargeNumber(revenue.greenRevenue) : "$0"}
                </div>
                <div className="text-sm text-gray-400">Green</div>
              </div>
            </div>
          </div>

          {/* Carbon Emissions Saved */}
          <div>
            <h4 className="text-lg font-semibold mb-4 flex items-center">
              <Leaf className="w-4 h-4 mr-2 text-green-400" />
              Carbon Emissions Saved
            </h4>
            <div className="flex items-center space-x-4">
              <div className="text-2xl font-bold text-green-400">
                {carbon
                  ? `${formatLargeNumber(carbon.carbonSaved)} tons`
                  : "0 tons"}
              </div>
              <div className="text-green-400">
                <TrendingUp className="w-5 h-5" />
              </div>
            </div>
          </div>
        </div>

        {/* Chart Container */}
        <div className="chart-container rounded-lg p-4 mb-6">
          <ResponsiveContainer width="100%" height={280}>
            <LineChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="rgba(255, 255, 255, 0.1)"
              />
              <XAxis
                dataKey="month"
                tick={{ fill: "#D4AF37", fontSize: 11, fontWeight: 500 }}
                axisLine={{ stroke: "#D4AF37", strokeWidth: 1 }}
                tickLine={{ stroke: "#D4AF37" }}
              />
              <YAxis
                tick={{ fill: "#D4AF37", fontSize: 11, fontWeight: 500 }}
                axisLine={{ stroke: "#D4AF37", strokeWidth: 1 }}
                tickLine={{ stroke: "#D4AF37" }}
                tickFormatter={(value) => `$${(value / 1000000).toFixed(1)}M`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#0A0A0A",
                  border: "2px solid #D4AF37",
                  borderRadius: "12px",
                  color: "#FFFFFF",
                  boxShadow: "0 8px 32px rgba(212, 175, 55, 0.3)",
                }}
                formatter={(value: number, name: string) => [
                  formatCurrency(value),
                  name === "blackRevenue"
                    ? "Black Token Revenue"
                    : "Green Token Revenue",
                ]}
                labelStyle={{ color: "#D4AF37", fontWeight: "bold" }}
              />
              <Legend
                wrapperStyle={{ color: "#FFFFFF", paddingTop: "20px" }}
                iconType="circle"
              />
              <Line
                type="monotone"
                dataKey="blackRevenue"
                stroke="#FFFFFF"
                strokeWidth={3}
                dot={{
                  fill: "#FFFFFF",
                  strokeWidth: 2,
                  r: 5,
                  stroke: "#D4AF37",
                }}
                activeDot={{
                  r: 7,
                  fill: "#D4AF37",
                  stroke: "#FFFFFF",
                  strokeWidth: 2,
                }}
                name="Black Token Revenue"
              />
              <Line
                type="monotone"
                dataKey="greenRevenue"
                stroke="#10B981"
                strokeWidth={3}
                dot={{
                  fill: "#10B981",
                  strokeWidth: 2,
                  r: 5,
                  stroke: "#D4AF37",
                }}
                activeDot={{
                  r: 7,
                  fill: "#D4AF37",
                  stroke: "#10B981",
                  strokeWidth: 2,
                }}
                name="Green Token Revenue"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="p-3 rounded-lg bg-gray-800">
            <div className="text-lg font-bold">30 min</div>
            <div className="text-sm text-gray-400">Update Frequency</div>
          </div>
          <div className="p-3 rounded-lg bg-gray-800">
            <div className="text-lg font-bold">100</div>
            <div className="text-sm text-gray-400">Active Users</div>
          </div>
        </div>
      </div>
    </div>
  );
}
