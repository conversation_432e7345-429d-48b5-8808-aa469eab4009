interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
}

export function LoadingOverlay({ isVisible, message = "Processing transaction..." }: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 animate-fade-in">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-crypto-gold border-t-transparent rounded-full animate-spin mb-4"></div>
        <div className="text-crypto-gold font-medium">{message}</div>
      </div>
    </div>
  );
}
