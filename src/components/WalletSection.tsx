import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { useWalletBalances, useUpdateWalletBalance } from "@/hooks/useTokenData";
import { useMetaMask } from "@/hooks/useMetaMask";
import { formatNumber, formatCurrency, calculateEstimatedValue } from "@/lib/utils";
import { Wallet, FileText } from "lucide-react";

export function WalletSection() {
  const { account, isConnected } = useMetaMask();
  const { data: balances = [], isLoading } = useWalletBalances(account);
  const updateBalance = useUpdateWalletBalance();

  // Wallet balances are now fetched from actual blockchain/wallet data
  // No dummy data initialization needed

  if (!isConnected) {
    return (
      <div className="gradient-border h-full">
        <div className="gradient-border-content p-6 flex items-center justify-center">
          <div className="text-center text-gray-400">
            <Wallet className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Connect your wallet to view balances</p>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="gradient-border h-full">
        <div className="gradient-border-content p-6">
          <div className="flex items-center mb-6">
            <Wallet className="text-crypto-gold mr-2 h-5 w-5" />
            <h3 className="text-xl font-bold">Wallet</h3>
          </div>
          <div className="space-y-4 mb-6 animate-pulse">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="h-14 bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const estimatedValue = calculateEstimatedValue(balances);

  return (
    <div className="gradient-border h-full animate-slide-up" style={{ animationDelay: '0.2s' }}>
      <div className="gradient-border-content p-6">
        <h3 className="text-xl font-bold mb-6 flex items-center">
          <Wallet className="text-crypto-gold mr-2 h-5 w-5" />
          Wallet
        </h3>

        <div className="space-y-4 mb-6">
          {balances.map((balance) => {
            const isBlack = balance.token.tokenType === 'black';
            const gradientClass = isBlack 
              ? 'from-gray-800 to-black' 
              : 'from-green-800 to-green-900';

            return (
              <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors">
                <div className="flex items-center space-x-2 sm:space-x-3">
                  <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gradient-to-br ${gradientClass} border border-crypto-gold flex items-center justify-center text-xs`}>
                    <span className="text-crypto-gold font-bold text-xs">C</span>
                  </div>
                  <span className="font-medium text-sm sm:text-base truncate">{balance.token.name}</span>
                </div>
                <span className="font-bold text-sm sm:text-base">{formatNumber(balance.balance, 0)}</span>
              </div>
            );
          })}
        </div>

        <div className="border-t border-gray-700 pt-4 mb-6">
          <div className="text-gray-400 text-sm mb-1">Estimated Value</div>
          <div className="text-2xl font-bold text-crypto-gold">
            {formatCurrency(estimatedValue)}
          </div>
        </div>

        <div className="space-y-3">
          <Button 
            variant="outline" 
            className="w-full bg-gray-700 hover:bg-gray-600 border-gray-600"
            onClick={() => window.open('#', '_blank')}
          >
            <FileText className="w-4 h-4 mr-2" />
            Black Token Whitepaper
          </Button>
          <Button 
            variant="outline" 
            className="w-full bg-gray-700 hover:bg-gray-600 border-gray-600"
            onClick={() => window.open('#', '_blank')}
          >
            <FileText className="w-4 h-4 mr-2" />
            Green Token Whitepaper
          </Button>
        </div>
      </div>
    </div>
  );
}