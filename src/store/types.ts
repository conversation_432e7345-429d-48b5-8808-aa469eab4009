import type { Token, Wallet<PERSON>alance, Analytics } from "@/types/token";

export interface TokenState {
  tokens: Token[];
  blackToken: Token | null;
  greenToken: Token | null;
  loading: boolean;
  error: string | null;
}

export interface WalletState {
  balances: (WalletBalance & { token: Token })[];
  loading: boolean;
  error: string | null;
  estimatedValue: string;
}

export interface AnalyticsState {
  blackAnalytics: Analytics[];
  greenAnalytics: Analytics[];
  totalRevenue: { blackRevenue: string; greenRevenue: string } | null;
  totalCarbonSaved: string;
  loading: boolean;
  error: string | null;
}

export interface TransactionState {
  pending: boolean;
  error: string | null;
  lastTransaction: any | null;
}

export interface MetaMaskState {
  isConnected: boolean;
  account: string | null;
  isConnecting: boolean;
  error: string | null;
}

export interface RootState {
  tokens: TokenState;
  wallet: WalletState;
  analytics: AnalyticsState;
  transactions: TransactionState;
  metamask: MetaMaskState;
  multiWallet: import("@/types/wallet").WalletState;
}
