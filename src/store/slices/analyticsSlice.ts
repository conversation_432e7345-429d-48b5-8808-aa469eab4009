import { createSlice } from '@reduxjs/toolkit';
import type { AnalyticsState } from '../types';
import {
  fetchAnalytics,
  fetchAnalyticsSuccess,
  fetchAnalyticsFailure,
  fetchTotalRevenue,
  fetchTotalRevenueSuccess,
  fetchTotalRevenueFailure,
  fetchTotalCarbonSaved,
  fetchTotalCarbonSavedSuccess,
  fetchTotalCarbonSavedFailure
} from '../actions';

const initialState: AnalyticsState = {
  blackAnalytics: [],
  greenAnalytics: [],
  totalRevenue: null,
  totalCarbonSaved: '0',
  loading: false,
  error: null,
};

const analyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchAnalytics, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAnalyticsSuccess, (state, action) => {
        state.loading = false;
        if (action.payload.tokenId === 1) {
          state.blackAnalytics = action.payload.data;
        } else if (action.payload.tokenId === 2) {
          state.greenAnalytics = action.payload.data;
        }
      })
      .addCase(fetchAnalyticsFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(fetchTotalRevenue, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTotalRevenueSuccess, (state, action) => {
        state.loading = false;
        state.totalRevenue = action.payload;
      })
      .addCase(fetchTotalRevenueFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(fetchTotalCarbonSaved, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTotalCarbonSavedSuccess, (state, action) => {
        state.loading = false;
        state.totalCarbonSaved = action.payload;
      })
      .addCase(fetchTotalCarbonSavedFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default analyticsSlice.reducer;