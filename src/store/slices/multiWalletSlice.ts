import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { WalletState, WalletProvider } from '@/types/wallet';

const initialState: WalletState = {
  isConnected: false,
  account: null,
  provider: null,
  isConnecting: false,
  error: null,
  chainId: undefined,
};

const multiWalletSlice = createSlice({
  name: 'multiWallet',
  initialState,
  reducers: {
    setConnecting: (state, action: PayloadAction<boolean>) => {
      state.isConnecting = action.payload;
      state.error = null;
    },
    connectSuccess: (state, action: PayloadAction<{ account: string; provider: WalletProvider; chainId?: string }>) => {
      state.isConnected = true;
      state.account = action.payload.account;
      state.provider = action.payload.provider;
      state.chainId = action.payload.chainId;
      state.isConnecting = false;
      state.error = null;
    },
    connectFailure: (state, action: PayloadAction<string>) => {
      state.isConnected = false;
      state.account = null;
      state.provider = null;
      state.isConnecting = false;
      state.error = action.payload;
    },
    disconnect: (state) => {
      state.isConnected = false;
      state.account = null;
      state.provider = null;
      state.isConnecting = false;
      state.error = null;
      state.chainId = undefined;
    },
    updateAccount: (state, action: PayloadAction<string>) => {
      state.account = action.payload;
      state.isConnected = true;
    },
    updateChainId: (state, action: PayloadAction<string>) => {
      state.chainId = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setConnecting,
  connectSuccess,
  connectFailure,
  disconnect,
  updateAccount,
  updateChainId,
  clearError,
} = multiWalletSlice.actions;

export default multiWalletSlice.reducer;