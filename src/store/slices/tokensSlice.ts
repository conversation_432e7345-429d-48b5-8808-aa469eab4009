import { createSlice } from '@reduxjs/toolkit';
import type { TokenState } from '../types';
import {
  fetchTokens,
  fetchTokensSuccess,
  fetchTokensFailure,
  fetchTokenByType,
  fetchTokenByTypeSuccess,
  fetchTokenByTypeFailure
} from '../actions';

const initialState: TokenState = {
  tokens: [],
  blackToken: null,
  greenToken: null,
  loading: false,
  error: null,
};

const tokensSlice = createSlice({
  name: 'tokens',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch all tokens
      .addCase(fetchTokens, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTokensSuccess, (state, action) => {
        state.loading = false;
        state.tokens = action.payload;
        state.blackToken = action.payload.find(t => t.tokenType === 'black') || null;
        state.greenToken = action.payload.find(t => t.tokenType === 'green') || null;
      })
      .addCase(fetchTokensFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch token by type
      .addCase(fetchTokenByType, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTokenByTypeSuccess, (state, action) => {
        state.loading = false;
        if (action.payload.type === 'black') {
          state.blackToken = action.payload.token;
        } else {
          state.greenToken = action.payload.token;
        }
      })
      .addCase(fetchTokenByTypeFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default tokensSlice.reducer;