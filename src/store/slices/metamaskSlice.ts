import { createSlice } from "@reduxjs/toolkit";
import type { MetaMaskState } from "../types";
import {
  connectWallet,
  connectWalletSuccess,
  connectWalletFailure,
  disconnectWallet,
  setConnecting,
  updateAccount,
} from "../actions";

const initialState: MetaMaskState = {
  isConnected: false,
  account: null,
  isConnecting: false,
  error: null,
};

const metamaskSlice = createSlice({
  name: "metamask",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(connectWallet, (state) => {
        state.isConnecting = true;
        state.error = null;
      })
      .addCase(connectWalletSuccess, (state, action) => {
        state.isConnecting = false;
        state.isConnected = true;
        state.account = action.payload.account;
        state.error = null;
      })
      .addCase(connectWalletFailure, (state, action) => {
        state.isConnecting = false;
        state.isConnected = false;
        state.account = null;
        state.error = action.payload;
      })
      .addCase(disconnectWallet, (state) => {
        state.isConnected = false;
        state.account = null;
        state.isConnecting = false;
        state.error = null;
      })
      .addCase(setConnecting, (state, action) => {
        state.isConnecting = action.payload;
      })
      .addCase(updateAccount, (state, action) => {
        state.account = action.payload;
        state.isConnected = true;
        state.error = null;
      });
  },
});

export default metamaskSlice.reducer;
