import { createSlice } from '@reduxjs/toolkit';
import type { TransactionState } from '../types';
import {
  executeTokenAction,
  executeTokenActionSuccess,
  executeTokenActionFailure
} from '../actions';

const initialState: TransactionState = {
  pending: false,
  error: null,
  lastTransaction: null,
};

const transactionsSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    clearTransactionError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(executeTokenAction, (state) => {
        state.pending = true;
        state.error = null;
      })
      .addCase(executeTokenActionSuccess, (state, action) => {
        state.pending = false;
        state.lastTransaction = action.payload;
      })
      .addCase(executeTokenActionFailure, (state, action) => {
        state.pending = false;
        state.error = action.payload;
      });
  },
});

export const { clearTransactionError } = transactionsSlice.actions;
export default transactionsSlice.reducer;