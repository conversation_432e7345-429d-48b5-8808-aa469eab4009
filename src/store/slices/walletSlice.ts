import { createSlice } from '@reduxjs/toolkit';
import type { WalletState } from '../types';
import {
  fetchWalletBalances,
  fetchWalletBalancesSuccess,
  fetchWalletBalancesFailure,
  updateWalletBalance,
  updateWalletBalanceSuccess,
  updateWalletBalanceFailure
} from '../actions';

const initialState: WalletState = {
  balances: [],
  loading: false,
  error: null,
  estimatedValue: '0',
};

const walletSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {
    calculateEstimatedValue: (state) => {
      const total = state.balances.reduce((sum, balance) => {
        const balanceNum = parseFloat(balance.balance);
        const priceNum = parseFloat(balance.token.currentPrice);
        return sum + (balanceNum * priceNum);
      }, 0);
      state.estimatedValue = total.toString();
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWalletBalances, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWalletBalancesSuccess, (state, action) => {
        state.loading = false;
        state.balances = action.payload;
        const total = action.payload.reduce((sum, balance) => {
          const balanceNum = parseFloat(balance.balance);
          const priceNum = parseFloat(balance.token.currentPrice);
          return sum + (balanceNum * priceNum);
        }, 0);
        state.estimatedValue = total.toString();
      })
      .addCase(fetchWalletBalancesFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(updateWalletBalance, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateWalletBalanceSuccess, (state, action) => {
        state.loading = false;
        const existingIndex = state.balances.findIndex(
          b => b.walletAddress === action.payload.walletAddress && 
               b.tokenId === action.payload.tokenId
        );
        if (existingIndex >= 0) {
          state.balances[existingIndex] = { ...state.balances[existingIndex], ...action.payload };
        }
      })
      .addCase(updateWalletBalanceFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { calculateEstimatedValue } = walletSlice.actions;
export default walletSlice.reducer;