import { all, fork } from 'redux-saga/effects';
import { tokenSagas } from './tokenSagas';
import { walletSagas } from './walletSagas';
import { analyticsSagas } from './analyticsSagas';
import { transactionSagas } from './transactionSagas';
import { metamaskSagas } from './metamaskSagas';

export default function* rootSaga() {
  yield all([
    fork(tokenSagas),
    fork(walletSagas),
    fork(analyticsSagas),
    fork(transactionSagas),
    fork(metamaskSagas),
  ]);
}