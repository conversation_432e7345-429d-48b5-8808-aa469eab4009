import { call, put, takeEvery } from 'redux-saga/effects';
import {
  connectWallet,
  connectWalletSuccess,
  connectWalletFailure,
  updateAccount,
  fetchWalletBalances
} from '../actions';

declare global {
  interface Window {
    ethereum?: {
      request: (args: { method: string; params?: any[] }) => Promise<any>;
      on: (event: string, handler: (accounts: string[]) => void) => void;
      removeListener: (event: string, handler: (accounts: string[]) => void) => void;
      isMetaMask?: boolean;
    };
  }
}

function* connectWalletSaga() {
  try {
    if (!window.ethereum) {
      throw new Error("MetaMask is not installed. Please install MetaMask to continue.");
    }

    const accounts: string[] = yield call([window.ethereum, 'request'], {
      method: "eth_requestAccounts",
    });

    if (accounts.length > 0) {
      yield put(connectWalletSuccess(accounts[0]));
      yield put(fetchWalletBalances(accounts[0]));
    } else {
      throw new Error("No accounts found");
    }
  } catch (error: any) {
    yield put(connectWalletFailure(error.message || "Failed to connect to MetaMask"));
  }
}

function* checkExistingConnection() {
  try {
    if (window.ethereum) {
      const accounts: string[] = yield call([window.ethereum, 'request'], {
        method: "eth_accounts",
      });
      
      if (accounts.length > 0) {
        yield put(updateAccount(accounts[0]));
        yield put(fetchWalletBalances(accounts[0]));
      }
    }
  } catch (error) {
    console.error("Error checking MetaMask connection:", error);
  }
}

export function* metamaskSagas() {
  yield takeEvery(connectWallet.type, connectWalletSaga);
  // Check for existing connection on app start
  yield call(checkExistingConnection);
}