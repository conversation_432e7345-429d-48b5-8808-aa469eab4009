import { call, put, takeEvery, takeLatest } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import {
  fetchAnalytics,
  fetchAnalyticsSuccess,
  fetchAnalyticsFailure,
  fetchTotalRevenue,
  fetchTotalRevenueSuccess,
  fetchTotalRevenueFailure,
  fetchTotalCarbonSaved,
  fetchTotalCarbonSavedSuccess,
  fetchTotalCarbonSavedFailure,
} from "../actions";
import api from "@/lib/api";
import type { Analytics } from "@/types/token";

function* fetchAnalyticsSaga(action: PayloadAction<number>) {
  try {
    const data: Analytics[] = yield call(
      api.get,
      `/analytics/${action.payload}`
    );
    yield put(fetchAnalyticsSuccess({ tokenId: action.payload, data }));
  } catch (error: any) {
    yield put(fetchAnalyticsFailure(error.message));
  }
}

function* fetchTotalRevenueSaga() {
  try {
    const revenue: { blackRevenue: string; greenRevenue: string } = yield call(
      api.get,
      "/analytics/revenue/total"
    );
    yield put(fetchTotalRevenueSuccess(revenue));
  } catch (error: any) {
    yield put(fetchTotalRevenueFailure(error.message));
  }
}

function* fetchTotalCarbonSavedSaga() {
  try {
    const response: { carbonSaved: string } = yield call(
      api.get,
      "/analytics/carbon/total"
    );
    yield put(fetchTotalCarbonSavedSuccess(response.carbonSaved));
  } catch (error: any) {
    yield put(fetchTotalCarbonSavedFailure(error.message));
  }
}

export function* analyticsSagas() {
  yield takeEvery(fetchAnalytics.type, fetchAnalyticsSaga);
  yield takeLatest(fetchTotalRevenue.type, fetchTotalRevenueSaga);
  yield takeLatest(fetchTotalCarbonSaved.type, fetchTotalCarbonSavedSaga);
}
