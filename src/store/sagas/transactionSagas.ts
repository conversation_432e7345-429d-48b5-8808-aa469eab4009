import { call, put, takeEvery } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import {
  executeTokenAction,
  executeTokenActionSuccess,
  executeTokenActionFailure,
  fetchWalletBalances,
  fetchTokens,
} from "../actions";

async function apiRequest(method: string, url: string, data?: any) {
  const config: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
    },
  };

  if (data) {
    config.body = JSON.stringify(data);
  }

  const response = await fetch(url, config);
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error || `API call failed: ${response.statusText}`
    );
  }
  return response.json();
}

function* executeTokenActionSaga(
  action: PayloadAction<{
    tokenId: number;
    action: "buy" | "sell" | "trade" | "burn";
    walletAddress: string;
    amount: string;
  }>
): Generator<any, void, any> {
  try {
    const {
      tokenId,
      action: actionType,
      walletAddress,
      amount,
    } = action.payload;

    const result = yield call(
      apiRequest,
      "POST",
      `/api/tokens/${tokenId}/${actionType}`,
      { walletAddress, amount }
    );

    yield put(executeTokenActionSuccess(result));

    // Refresh wallet balances and tokens after successful transaction
    yield put(fetchWalletBalances(walletAddress));
    yield put(fetchTokens());
  } catch (error: any) {
    yield put(executeTokenActionFailure(error.message));
  }
}

export function* transactionSagas() {
  yield takeEvery(executeTokenAction.type, executeTokenActionSaga);
}
