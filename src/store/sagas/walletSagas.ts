import { call, put, takeEvery, takeLatest } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import {
  fetchWalletBalances,
  fetchWalletBalancesSuccess,
  fetchWalletBalancesFailure,
  updateWalletBalance,
  updateWalletBalanceSuccess,
  updateWalletBalanceFailure,
} from "../actions";
import api from "@/lib/api";
import type { Token, WalletBalance } from "@/types/token";

function* fetchWalletBalancesSaga(action: PayloadAction<string>) {
  try {
    const balances: (WalletBalance & { token: Token })[] = yield call(
      api.get,
      `/wallet/${action.payload}/balances`
    );
    yield put(fetchWalletBalancesSuccess(balances));
  } catch (error: any) {
    yield put(fetchWalletBalancesFailure(error.message));
  }
}

function* updateWalletBalanceSaga(
  action: PayloadAction<{
    walletAddress: string;
    tokenId: number;
    balance: string;
  }>
) {
  try {
    const { walletAddress, tokenId, balance } = action.payload;
    const updatedBalance: WalletBalance = yield call(
      api.post,
      `/wallet/${walletAddress}/balance`,
      { tokenId, balance }
    );
    yield put(updateWalletBalanceSuccess(updatedBalance));
  } catch (error: any) {
    yield put(updateWalletBalanceFailure(error.message));
  }
}

export function* walletSagas() {
  yield takeLatest(fetchWalletBalances.type, fetchWalletBalancesSaga);
  yield takeEvery(updateWalletBalance.type, updateWalletBalanceSaga);
}
