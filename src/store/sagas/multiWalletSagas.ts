
import { call, put, takeEvery, select } from 'redux-saga/effects';
import { PayloadAction } from '@reduxjs/toolkit';
import {
  connectWallet,
  disconnectWallet,
  setConnecting,
  connectSuccess,
  connectFailure,
  updateAccount,
} from '@/store/actions';
import { getAllWalletProviders } from '@/lib/walletProviders';
import type { WalletProvider } from '@/types/wallet';
import type { RootState } from '@/store/types';

function* connectWalletSaga(action: PayloadAction<{ provider: WalletProvider }>): Generator<any, void, any> {
  try {
    yield put(setConnecting(true));
    
    const { provider } = action.payload;
    
    if (!provider.isInstalled) {
      const installUrls: Record<string, string> = {
        metamask: 'https://metamask.io/download/',
        phantom: 'https://phantom.app/',
        rabby: 'https://rabby.io/',
      };
      
      if (installUrls[provider.id]) {
        if (typeof window !== 'undefined') {
          window.open(installUrls[provider.id], '_blank');
        }
        yield put(connectFailure(`${provider.name} is not installed. Please install it first.`));
        return;
      }
    }
    
    const account: string = yield call([provider, 'connect']);
    
    let chainId: string | undefined;
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        chainId = yield call([window.ethereum, 'request'], {
          method: 'eth_chainId',
        });
      } catch (error) {
        console.warn('Could not get chain ID:', error);
      }
    }
    
    yield put(connectSuccess({ account, provider, chainId }));
    
  } catch (error: any) {
    yield put(connectFailure(error.message || `Failed to connect to wallet`));
  }
}

function* disconnectWalletSaga(): Generator<any, void, any> {
  try {
    const state: RootState = yield select();
    const { provider } = state.multiWallet;
    
    if (provider && provider.disconnect) {
      try {
        yield call([provider, 'disconnect']);
      } catch (error) {
        console.warn('Provider disconnect failed:', error);
      }
    }
    
    // Remove event listeners
    if (typeof window !== 'undefined' && window.ethereum) {
      if (window.ethereum.removeAllListeners) {
        window.ethereum.removeAllListeners('accountsChanged');
        window.ethereum.removeAllListeners('chainChanged');
      }
    }
    
    // Clear localStorage items
    if (typeof window !== 'undefined') {
      localStorage.removeItem('walletconnect');
      localStorage.removeItem('WALLETCONNECT_DEEPLINK_CHOICE');
    }
    
  } catch (error) {
    console.warn('Error during wallet disconnect:', error);
  }
}

function* checkExistingConnectionSaga(): Generator<any, void, any> {
  try {
    const providers = getAllWalletProviders();
    
    for (const provider of providers) {
      if (provider.isInstalled) {
        try {
          const accounts: string[] = yield call([provider, 'getAccounts']);
          if (accounts.length > 0) {
            let chainId: string | undefined;
            if (typeof window !== 'undefined' && window.ethereum) {
              try {
                chainId = yield call([window.ethereum, 'request'], {
                  method: 'eth_chainId',
                });
              } catch (error) {
                console.warn('Could not get chain ID:', error);
              }
            }
            
            yield put(connectSuccess({ 
              account: accounts[0], 
              provider, 
              chainId 
            }));
            break;
          }
        } catch (error) {
          console.warn(`Error checking ${provider.name}:`, error);
        }
      }
    }
  } catch (error) {
    console.warn('Error checking existing connections:', error);
  }
}

export function* multiWalletSagas() {
  yield takeEvery(connectWallet.type, connectWalletSaga);
  yield takeEvery(disconnectWallet.type, disconnectWalletSaga);
  
  // Check for existing connections on app start
  yield call(checkExistingConnectionSaga);
}
