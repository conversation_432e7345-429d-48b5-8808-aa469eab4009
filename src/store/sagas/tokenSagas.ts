import { call, put, takeEvery, takeLatest } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import {
  fetchTokens,
  fetchTokensSuccess,
  fetchTokensFailure,
  fetchTokenByType,
  fetchTokenByTypeSuccess,
  fetchTokenByTypeFailure,
} from "../actions";
import api from "@/lib/api";
import type { Token } from "@/types/token";

function* fetchTokensSaga() {
  try {
    const tokens: Token[] = yield call(api.get, "/tokens");
    yield put(fetchTokensSuccess(tokens));
  } catch (error: any) {
    yield put(fetchTokensFailure(error.message));
  }
}

function* fetchTokenByTypeSaga(action: PayloadAction<"black" | "green">) {
  try {
    const token: Token = yield call(api.get, `/tokens/${action.payload}`);
    yield put(fetchTokenByTypeSuccess({ type: action.payload, token }));
  } catch (error: any) {
    yield put(fetchTokenByTypeFailure(error.message));
  }
}

export function* tokenSagas() {
  yield takeEvery(fetchTokens.type, fetchTokensSaga);
  yield takeLatest(fetchTokenByType.type, fetchTokenByTypeSaga);
}
