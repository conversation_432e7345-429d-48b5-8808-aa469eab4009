import { createAction } from "@reduxjs/toolkit";
import type { Token, WalletBalance, Analytics } from "@/types/token";

// Token Actions
export const fetchTokens = createAction("tokens/fetchTokens");
export const fetchTokensSuccess = createAction<Token[]>(
  "tokens/fetchTokensSuccess"
);
export const fetchTokensFailure = createAction<string>(
  "tokens/fetchTokensFailure"
);

export const fetchTokenByType = createAction<"black" | "green">(
  "tokens/fetchTokenByType"
);
export const fetchTokenByTypeSuccess = createAction<{
  type: "black" | "green";
  token: Token;
}>("tokens/fetchTokenByTypeSuccess");
export const fetchTokenByTypeFailure = createAction<string>(
  "tokens/fetchTokenByTypeFailure"
);

// Wallet Actions
export const fetchWalletBalances = createAction<string>(
  "wallet/fetchWalletBalances"
);
export const fetchWalletBalancesSuccess = createAction<
  (WalletBalance & { token: Token })[]
>("wallet/fetchWalletBalancesSuccess");
export const fetchWalletBalancesFailure = createAction<string>(
  "wallet/fetchWalletBalancesFailure"
);

export const updateWalletBalance = createAction<{
  walletAddress: string;
  tokenId: number;
  balance: string;
}>("wallet/updateWalletBalance");
export const updateWalletBalanceSuccess = createAction<WalletBalance>(
  "wallet/updateWalletBalanceSuccess"
);
export const updateWalletBalanceFailure = createAction<string>(
  "wallet/updateWalletBalanceFailure"
);

// Analytics Actions
export const fetchAnalytics = createAction<number>("analytics/fetchAnalytics");
export const fetchAnalyticsSuccess = createAction<{
  tokenId: number;
  data: Analytics[];
}>("analytics/fetchAnalyticsSuccess");
export const fetchAnalyticsFailure = createAction<string>(
  "analytics/fetchAnalyticsFailure"
);

export const fetchTotalRevenue = createAction("analytics/fetchTotalRevenue");
export const fetchTotalRevenueSuccess = createAction<{
  blackRevenue: string;
  greenRevenue: string;
}>("analytics/fetchTotalRevenueSuccess");
export const fetchTotalRevenueFailure = createAction<string>(
  "analytics/fetchTotalRevenueFailure"
);

export const fetchTotalCarbonSaved = createAction(
  "analytics/fetchTotalCarbonSaved"
);
export const fetchTotalCarbonSavedSuccess = createAction<string>(
  "analytics/fetchTotalCarbonSavedSuccess"
);
export const fetchTotalCarbonSavedFailure = createAction<string>(
  "analytics/fetchTotalCarbonSavedFailure"
);

// Transaction Actions
export const executeTokenAction = createAction<{
  tokenId: number;
  action: "buy" | "sell" | "trade" | "burn";
  walletAddress: string;
  amount: string;
}>("transactions/executeTokenAction");

export const executeTokenActionSuccess = createAction<any>(
  "transactions/executeTokenActionSuccess"
);
export const executeTokenActionFailure = createAction<string>(
  "transactions/executeTokenActionFailure"
);

// MultiWallet Actions
export const connectWallet = createAction<{ provider: any }>(
  "multiWallet/connectWallet"
);
export const connectWalletSuccess = createAction<{
  account: string;
  provider: any;
  chainId?: string;
}>("multiWallet/connectSuccess");
export const connectWalletFailure = createAction<string>(
  "multiWallet/connectFailure"
);
export const connectSuccess = createAction<{
  account: string;
  provider: any;
  chainId?: string;
}>("multiWallet/connectSuccess");
export const connectFailure = createAction<string>(
  "multiWallet/connectFailure"
);

export const disconnectWallet = createAction("multiWallet/disconnect");
export const setConnecting = createAction<boolean>("multiWallet/setConnecting");
export const updateAccount = createAction<string>("multiWallet/updateAccount");
export const updateChainId = createAction<string>("multiWallet/updateChainId");
