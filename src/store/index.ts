import { configureStore } from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';
import tokensReducer from './slices/tokensSlice';
import walletReducer from './slices/walletSlice';
import analyticsReducer from './slices/analyticsSlice';
import transactionsReducer from './slices/transactionsSlice';
import metamaskReducer from './slices/metamaskSlice';
import multiWalletReducer from './slices/multiWalletSlice';
import rootSaga from './sagas';
import type { RootState } from './types';

const sagaMiddleware = createSagaMiddleware();

export const store = configureStore({
  reducer: {
    tokens: tokensReducer,
    wallet: walletReducer,
    analytics: analyticsReducer,
    transactions: transactionsReducer,
    metamask: metamaskReducer,
    multiWallet: multiWalletReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      thunk: false,
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }).concat(sagaMiddleware),
});

sagaMiddleware.run(rootSaga);

export type { RootState };
export type AppDispatch = typeof store.dispatch;