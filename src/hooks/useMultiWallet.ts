
import { useState, useEffect } from "react";
import { useSelector, useDispatch } from 'react-redux';
import type { RootState, AppDispatch } from '@/store';
import type { WalletProvider } from '@/types/wallet';
import {
  connectWallet,
  disconnectWallet,
  updateAccount,
  setConnecting,
  connectSuccess,
  connectFailure
} from '@/store/actions';
import { getAllWalletProviders } from '@/lib/walletProviders';
import { useToast } from '@/hooks/use-toast';

export function useMultiWallet() {
  const dispatch = useDispatch<AppDispatch>();
  const { isConnected, account, provider, isConnecting, error } = useSelector((state: RootState) => state.multiWallet);
  const [showWalletModal, setShowWalletModal] = useState(false);
  const { toast } = useToast();

  const handleConnectWallet = async (walletProvider: WalletProvider) => {
    try {
      dispatch(setConnecting(true));
      
      if (!walletProvider.isInstalled) {
        // Open installation page for the wallet
        const installUrls: Record<string, string> = {
          metamask: 'https://metamask.io/download/',
          phantom: 'https://phantom.app/',
          rabby: 'https://rabby.io/',
        };
        
        if (installUrls[walletProvider.id]) {
          window.open(installUrls[walletProvider.id], '_blank');
          dispatch(connectFailure(`${walletProvider.name} is not installed. Please install it first.`));
          return;
        }
      }
      
      const account = await walletProvider.connect();
      
      // Get chain ID if available
      let chainId: string | undefined;
      if (typeof window !== 'undefined' && window.ethereum) {
        try {
          chainId = await window.ethereum.request({
            method: 'eth_chainId',
          });
        } catch (error) {
          console.warn('Could not get chain ID:', error);
        }
      }
      
      dispatch(connectSuccess({ account, provider: walletProvider, chainId }));
      setShowWalletModal(false);
      
    } catch (error: any) {
      dispatch(connectFailure(error.message || `Failed to connect to ${walletProvider.name}`));
    }
  };

  const handleDisconnectWallet = async () => {
    try {
      // Dispatch the disconnect action to trigger the saga
      dispatch(disconnectWallet());
      
      // Show user notification
      toast({
        title: "Wallet Disconnected",
        description: `Disconnected from ${provider?.name || 'wallet'}. You may need to manually disconnect from the wallet extension if desired.`,
      });
      
    } catch (error) {
      console.warn('Error during disconnect:', error);
      // Still dispatch disconnect action
      dispatch(disconnectWallet());
      
      toast({
        title: "Disconnected", 
        description: "Wallet disconnected from METWAVE.",
      });
    }
  };

  const openWalletModal = () => {
    setShowWalletModal(true);
  };

  const closeWalletModal = () => {
    setShowWalletModal(false);
  };

  // Check for existing connections on mount
  useEffect(() => {
    const checkExistingConnections = async () => {
      const providers = getAllWalletProviders();
      
      for (const walletProvider of providers) {
        if (walletProvider.isInstalled) {
          try {
            const accounts = await walletProvider.getAccounts();
            if (accounts.length > 0) {
              let chainId: string | undefined;
              if (typeof window !== 'undefined' && window.ethereum) {
                try {
                  chainId = await window.ethereum.request({
                    method: 'eth_chainId',
                  });
                } catch (error) {
                  console.warn('Could not get chain ID:', error);
                }
              }
              
              dispatch(connectSuccess({ 
                account: accounts[0], 
                provider: walletProvider, 
                chainId 
              }));
              break; // Use the first connected wallet found
            }
          } catch (error) {
            console.warn(`Error checking ${walletProvider.name}:`, error);
          }
        }
      }
    };

    if (!isConnected) {
      checkExistingConnections();
    }
  }, [dispatch, isConnected]);

  // Set up account change listeners
  useEffect(() => {
    const handleAccountsChanged = (accounts: string[]) => {
      if (accounts.length > 0) {
        dispatch(updateAccount(accounts[0]));
      } else {
        dispatch(disconnectWallet());
      }
    };

    const handleChainChanged = () => {
      // Reload page on chain change to ensure app state consistency
      window.location.reload();
    };

    if (typeof window !== 'undefined' && window.ethereum && isConnected) {
      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);
      
      return () => {
        if (window.ethereum && window.ethereum.removeListener) {
          window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
          window.ethereum.removeListener('chainChanged', handleChainChanged);
        }
      };
    }
  }, [dispatch, isConnected]);

  return {
    isConnected,
    account,
    provider,
    isConnecting,
    error,
    showWalletModal,
    connectWallet: handleConnectWallet,
    disconnectWallet: handleDisconnectWallet,
    openWalletModal,
    closeWalletModal,
  };
}
