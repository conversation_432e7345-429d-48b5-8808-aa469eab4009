import { useEffect } from "react";
import { useSelector, useDispatch } from 'react-redux';
import type { RootState, AppDispatch } from '@/store';
import {
  connectWallet,
  disconnectWallet,
  updateAccount
} from '@/store/actions';

declare global {
  interface Window {
    ethereum?: {
      request: (args: { method: string; params?: any[] }) => Promise<any>;
      on: (event: string, handler: (accounts: string[]) => void) => void;
      removeListener: (event: string, handler: (accounts: string[]) => void) => void;
      isMetaMask?: boolean;
    };
  }
}

export function useMetaMask() {
  const dispatch = useDispatch<AppDispatch>();
  const { isConnected, account, isConnecting, error } = useSelector((state: RootState) => state.metamask);

  const handleConnectWallet = () => {
    if (!window.ethereum) {
      window.open("https://metamask.io/", "_blank");
      return;
    }
    dispatch(connectWallet());
  };

  const handleDisconnectWallet = () => {
    dispatch(disconnectWallet());
  };

  useEffect(() => {
    // Listen for account changes
    const handleAccountsChanged = (accounts: string[]) => {
      if (accounts.length > 0) {
        dispatch(updateAccount(accounts[0]));
      } else {
        dispatch(disconnectWallet());
      }
    };

    if (window.ethereum) {
      window.ethereum.on("accountsChanged", handleAccountsChanged);
      
      return () => {
        window.ethereum?.removeListener("accountsChanged", handleAccountsChanged);
      };
    }
  }, [dispatch]);

  return {
    isConnected,
    account,
    isConnecting,
    error,
    connectWallet: handleConnectWallet,
    disconnectWallet: handleDisconnectWallet,
    isMetaMaskInstalled: !!window.ethereum,
  };
}
