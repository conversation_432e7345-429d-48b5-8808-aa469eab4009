import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "@/store";
import {
  fetchTokens,
  fetchTokenByType,
  fetchWalletBalances,
  fetchAnalytics,
  fetchTotalRevenue,
  fetchTotalCarbonSaved,
  executeTokenAction,
  updateWalletBalance,
} from "@/store/actions";

export function useTokens() {
  const dispatch = useDispatch<AppDispatch>();
  const { tokens, loading, error } = useSelector(
    (state: RootState) => state.tokens
  );

  useEffect(() => {
    if (tokens.length === 0) {
      dispatch(fetchTokens());
    }
  }, [dispatch, tokens.length]);

  return { data: tokens, isLoading: loading, error };
}

export function useToken(type: "black" | "green") {
  const dispatch = useDispatch<AppDispatch>();
  const { blackToken, greenToken, loading, error } = useSelector(
    (state: RootState) => state.tokens
  );

  useEffect(() => {
    if (
      (type === "black" && !blackToken) ||
      (type === "green" && !greenToken)
    ) {
      dispatch(fetchTokenByType(type));
    }
  }, [dispatch, type, blackToken, greenToken]);

  return {
    data: type === "black" ? blackToken : greenToken,
    isLoading: loading,
    error,
  };
}

export function useWalletBalances(walletAddress: string | null) {
  const dispatch = useDispatch<AppDispatch>();
  const { balances, loading, error } = useSelector(
    (state: RootState) => state.wallet
  );

  useEffect(() => {
    if (walletAddress) {
      dispatch(fetchWalletBalances(walletAddress));
    }
  }, [dispatch, walletAddress]);

  return { data: balances, isLoading: loading, error };
}

export function useAnalytics(tokenId: number) {
  const dispatch = useDispatch<AppDispatch>();
  const { blackAnalytics, greenAnalytics, loading, error } = useSelector(
    (state: RootState) => state.analytics
  );

  useEffect(() => {
    dispatch(fetchAnalytics(tokenId));
  }, [dispatch, tokenId]);

  return {
    data: tokenId === 1 ? blackAnalytics : greenAnalytics,
    isLoading: loading,
    error,
  };
}

export function useTotalRevenue() {
  const dispatch = useDispatch<AppDispatch>();
  const { totalRevenue, loading, error } = useSelector(
    (state: RootState) => state.analytics
  );

  useEffect(() => {
    dispatch(fetchTotalRevenue());
  }, [dispatch]);

  return { data: totalRevenue, isLoading: loading, error };
}

export function useTotalCarbonSaved() {
  const dispatch = useDispatch<AppDispatch>();
  const { totalCarbonSaved, loading, error } = useSelector(
    (state: RootState) => state.analytics
  );

  useEffect(() => {
    dispatch(fetchTotalCarbonSaved());
  }, [dispatch]);

  return { data: { carbonSaved: totalCarbonSaved }, isLoading: loading, error };
}

export function useTokenAction() {
  const dispatch = useDispatch<AppDispatch>();
  const { pending, error } = useSelector(
    (state: RootState) => state.transactions
  );

  const mutateAsync = async (params: {
    tokenId: number;
    action: "buy" | "sell" | "trade" | "burn";
    walletAddress: string;
    amount: string;
  }) => {
    return new Promise((resolve, reject) => {
      dispatch(executeTokenAction(params));
      // Note: In a real implementation, you'd want to listen for success/failure actions
      // For now, we'll resolve immediately
      resolve(params);
    });
  };

  return { mutateAsync, isPending: pending, error };
}

export function useUpdateWalletBalance() {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error } = useSelector((state: RootState) => state.wallet);

  const mutate = (params: {
    walletAddress: string;
    tokenId: number;
    balance: string;
  }) => {
    dispatch(updateWalletBalance(params));
  };

  return { mutate, isLoading: loading, error };
}
