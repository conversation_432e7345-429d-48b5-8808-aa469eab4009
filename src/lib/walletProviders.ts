import type { WalletProvider } from '@/types/wallet';

declare global {
  interface Window {
    ethereum?: any;
    phantom?: any;
    rabby?: any;
  }
}

// MetaMask Provider
export const createMetaMaskProvider = (): WalletProvider => ({
  id: 'metamask',
  name: 'Meta<PERSON><PERSON>',
  icon: '🦊',
  description: 'Connect using MetaMask browser extension',
  isInstalled: typeof window !== 'undefined' && !!window.ethereum && !!(window.ethereum as any).isMetaMask,
  isAvailable: typeof window !== 'undefined' && !!window.ethereum,

  async connect() {
    if (!window.ethereum) {
      throw new Error('MetaMask is not installed');
    }

    const accounts = await window.ethereum.request({
      method: 'eth_requestAccounts',
    });

    return accounts[0];
  },

  async disconnect() {
    // MetaMask doesn't have a programmatic disconnect
    // Users need to disconnect manually from the extension
  },

  async getAccounts() {
    if (!window.ethereum) return [];

    return await window.ethereum.request({
      method: 'eth_accounts',
    });
  },

  async switchNetwork(chainId: string) {
    if (!window.ethereum) return;

    await window.ethereum.request({
      method: 'wallet_switchEthereumChain',
      params: [{ chainId }],
    });
  }
});

// Phantom Wallet Provider (Ethereum support)
export const createPhantomProvider = (): WalletProvider => ({
  id: 'phantom',
  name: 'Phantom',
  icon: '👻',
  description: 'Connect using Phantom wallet',
  isInstalled: typeof window !== 'undefined' && !!window.phantom?.ethereum,
  isAvailable: typeof window !== 'undefined' && !!window.phantom?.ethereum,

  async connect() {
    if (!window.phantom?.ethereum) {
      throw new Error('Phantom wallet is not installed');
    }

    const accounts = await window.phantom.ethereum.request({
      method: 'eth_requestAccounts',
    });

    return accounts[0];
  },

  async disconnect() {
    if (window.phantom?.ethereum) {
      await window.phantom.ethereum.request({
        method: 'eth_accounts',
        params: [{ eth_accounts: {} }],
      });
    }
  },

  async getAccounts() {
    if (!window.phantom?.ethereum) return [];

    return await window.phantom.ethereum.request({
      method: 'eth_accounts',
    });
  }
});

// Rabby Wallet Provider
export const createRabbyProvider = (): WalletProvider => ({
  id: 'rabby',
  name: 'Rabby Wallet',
  icon: '🐰',
  description: 'Connect using Rabby browser extension',
  isInstalled: typeof window !== 'undefined' && !!window.rabby,
  isAvailable: typeof window !== 'undefined' && (!!window.rabby || !!window.ethereum?.isRabby),

  async connect() {
    const provider = window.rabby || window.ethereum;
    if (!provider) {
      throw new Error('Rabby Wallet is not installed');
    }

    const accounts = await provider.request({
      method: 'eth_requestAccounts',
    });

    return accounts[0];
  },

  async disconnect() {
    // Rabby disconnect
  },

  async getAccounts() {
    const provider = window.rabby || window.ethereum;
    if (!provider) return [];

    return await provider.request({
      method: 'eth_accounts',
    });
  }
});

export const getAllWalletProviders = (): WalletProvider[] => [
  createMetaMaskProvider(),
  createPhantomProvider(),
  createRabbyProvider(),
];

export const getAvailableWallets = (): WalletProvider[] => {
  return getAllWalletProviders().filter(wallet => wallet.isAvailable);
};

export const getInstalledWallets = (): WalletProvider[] => {
  return getAllWalletProviders().filter(wallet => wallet.isInstalled);
};