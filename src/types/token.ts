import type { Token, WalletBalance, Transaction, Analytics } from "@shared/schema";

export interface TokenWithBalance extends Token {
  balance?: string;
}

export interface WalletData {
  balances: WalletBalance[];
  estimatedValue: string;
}

export interface AnalyticsData {
  blackRevenue: string;
  greenRevenue: string;
  carbonSaved: string;
  chartData: ChartDataPoint[];
}

export interface ChartDataPoint {
  date: string;
  blackRevenue: number;
  greenRevenue: number;
}

export interface MetaMaskState {
  isConnected: boolean;
  account: string | null;
  isConnecting: boolean;
  error: string | null;
}

export { Token, WalletBalance, Transaction, Analytics };
