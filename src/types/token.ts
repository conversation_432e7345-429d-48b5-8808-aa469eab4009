// Core token interface
export interface Token {
  id: number;
  name: string;
  symbol: string;
  type: "black" | "green";
  currentPrice: string;
  priceChange24h: string;
  marketCap: string;
  volume24h: string;
  totalSupply: string;
  circulatingSupply: string;
  description?: string;
  contractAddress?: string;
  decimals?: number;
  logoUrl?: string;
}

// Wallet balance interface
export interface WalletBalance {
  id: number;
  walletAddress: string;
  tokenId: number;
  balance: string;
  lastUpdated: string;
}

// Transaction interface
export interface Transaction {
  id: number;
  walletAddress: string;
  tokenId: number;
  type: "buy" | "sell" | "trade" | "burn";
  amount: string;
  price: string;
  timestamp: string;
  txHash?: string;
  status: "pending" | "completed" | "failed";
}

// Analytics interface
export interface Analytics {
  id: number;
  tokenId: number;
  date: string;
  revenue: string;
  carbonSaved?: string;
  volume: string;
  transactions: number;
}

// Extended interfaces
export interface TokenWithBalance extends Token {
  balance?: string;
}

export interface WalletData {
  balances: WalletBalance[];
  estimatedValue: string;
}

export interface AnalyticsData {
  blackRevenue: string;
  greenRevenue: string;
  carbonSaved: string;
  chartData: ChartDataPoint[];
}

export interface ChartDataPoint {
  date: string;
  blackRevenue: number;
  greenRevenue: number;
}

export interface MetaMaskState {
  isConnected: boolean;
  account: string | null;
  isConnecting: boolean;
  error: string | null;
}
