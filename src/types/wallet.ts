
export interface WalletProvider {
  id: string;
  name: string;
  icon: string;
  description: string;
  isInstalled: boolean;
  isAvailable: boolean;
  connect: () => Promise<string>;
  disconnect: () => Promise<void>;
  getAccounts: () => Promise<string[]>;
  switchNetwork?: (chainId: string) => Promise<void>;
}

export interface WalletState {
  isConnected: boolean;
  account: string | null;
  provider: WalletProvider | null;
  isConnecting: boolean;
  error: string | null;
  chainId?: string;
}

export type WalletType = 'metamask' | 'phantom' | 'rabby';

export interface WalletConnectOptions {
  projectId: string;
  chains: number[];
  methods: string[];
}
