@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;

  /* Custom Metawave colors */
  --crypto-dark: hsl(0, 0%, 3.9%);
  --crypto-gray: hsl(0, 0%, 10.2%);
  --crypto-gold: hsl(45, 100%, 51%);
  --crypto-green: hsl(120, 35%, 27%);
  --crypto-light: hsl(0, 0%, 98%);
}

.dark {
  --background: hsl(0, 0%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(0, 0%, 15.9%);
  --muted-foreground: hsl(0, 5%, 64.9%);
  --popover: hsl(0, 0%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(0, 0%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(0, 3.7%, 15.9%);
  --input: hsl(0, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(0, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(0, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(0, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-crypto-dark text-white;
    font-family: "Inter", sans-serif;
  }

  html {
    @apply dark;
  }
}

@layer components {
  .gradient-border {
    @apply relative p-0.5 rounded-xl;
    background: linear-gradient(
      45deg,
      var(--crypto-gold),
      #f4e4bc,
      var(--crypto-gold)
    );
  }

  .gradient-border-content {
    @apply bg-crypto-gray rounded-lg w-full h-full;
  }

  .token-card {
    perspective: 1000px;
    transform-style: preserve-3d;
  }

  .wallet-connected {
    background: linear-gradient(135deg, #10b981, #065f46);
  }

  .chart-container {
    background: linear-gradient(135deg, var(--crypto-gray), var(--crypto-dark));
  }

  /* Mobile optimizations */
  @media (max-width: 640px) {
    .token-action-button {
      @apply text-xs py-2 px-3;
    }

    .stats-grid {
      @apply grid-cols-1 gap-3;
    }

    .mobile-stack {
      @apply flex flex-col space-y-3;
    }

    /* Dialog mobile responsiveness */
    [data-radix-dialog-content] {
      width: 95vw !important;
      max-width: 400px !important;
      margin: 0 auto !important;
      max-height: 85vh !important;
      overflow-y: auto !important;
    }

    [data-radix-dialog-overlay] {
      padding: 1rem !important;
    }
  }

  /* Token animations */
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin {
    animation: spin 0.6s ease-in-out;
  }

  /* Enhanced button styles */
  .token-action-button {
    @apply bg-transparent text-crypto-gold border border-crypto-gold hover:bg-crypto-gold hover:text-black;
    transition: all 0.2s ease-in-out;
  }

  .token-action-button:hover {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }
}

@layer utilities {
  .animate-flip {
    animation: flip 0.6s ease-in-out;
  }

  .animate-pulse-gold {
    animation: pulse-gold 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-slide-up {
    animation: slide-up 0.5s ease-out;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-in;
  }

  .bg-crypto-dark {
    background-color: var(--crypto-dark);
  }

  .bg-crypto-gray {
    background-color: var(--crypto-gray);
  }

  .text-crypto-gold {
    color: var(--crypto-gold);
  }

  .bg-crypto-gold {
    background-color: var(--crypto-gold);
  }

  .border-crypto-gold {
    border-color: var(--crypto-gold);
  }

  .text-crypto-green {
    color: var(--crypto-green);
  }

  .bg-crypto-green {
    background-color: var(--crypto-green);
  }
}

@keyframes flip {
  0% {
    transform: rotateY(0deg) scale(1);
  }
  50% {
    transform: rotateY(90deg) scale(1.05);
  }
  100% {
    transform: rotateY(0deg) scale(1);
  }
}

@keyframes pulse-gold {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(212, 175, 55, 0);
  }
}

@keyframes slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
