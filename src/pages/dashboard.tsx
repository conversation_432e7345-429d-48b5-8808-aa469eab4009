import { Header } from "@/components/Header";
import { TokenCard } from "@/components/TokenCard";
import { WalletSection } from "@/components/WalletSection";
import { AnalyticsSection } from "@/components/AnalyticsSection";
import { LoadingOverlay } from "@/components/LoadingOverlay";
import { useTokenAction } from "@/hooks/useTokenData";

export default function Dashboard() {
  const tokenAction = useTokenAction();

  return (
    <div className="min-h-screen bg-crypto-dark">
        <Header />
        <main className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
          {/* Token Cards Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8 lg:mb-10">
            <TokenCard tokenType="black" />
            <TokenCard tokenType="green" />
          </div>

          {/* Wallet and Analytics Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            <WalletSection />
            <AnalyticsSection />
          </div>
        </main>
      </div>
  );
}